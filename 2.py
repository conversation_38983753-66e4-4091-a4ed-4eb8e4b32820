import numpy as np
import random

# 定义环境参数
GRID_SIZE = 10 # 10x10 的网格
NUM_DRONES = 4
MAX_STEPS = 50 # 每回合最大步数

# 无人机动作定义 (上, 下, 左, 右, 不动)
ACTIONS = {
    0: (-1, 0),  # 上
    1: (1, 0),   # 下
    2: (0, -1),  # 左
    3: (0, 1),   # 右
    4: (0, 0)    # 不动
}

class DroneEnv:
    def __init__(self):
        self.grid_size = GRID_SIZE
        self.num_drones = NUM_DRONES
        self.max_steps = MAX_STEPS
        self.current_steps = 0

        # 随机生成起始点和目标点
        self.start_positions = []
        self.target_positions = []
        self.drone_positions = [] # 当前无人机位置

        # 确保起始点和目标点不重叠，且在网格内
        all_possible_positions = [(r, c) for r in range(self.grid_size) for c in range(self.grid_size)]

        # 随机选择不重复的起始点和目标点
        chosen_positions = random.sample(all_possible_positions, self.num_drones * 2)

        for i in range(self.num_drones):
            self.start_positions.append(chosen_positions[i])
            self.target_positions.append(chosen_positions[i + self.num_drones])

        self.reset()

    def reset(self):
        self.drone_positions = list(self.start_positions) # 重置无人机到起始点
        self.current_steps = 0
        self.done = [False] * self.num_drones # 标记每个无人机是否到达目标

        # 返回初始观测
        observations = self._get_observations()
        return observations

    def _get_observations(self):
        # 每个无人机的观测：自己的位置 (x, y), 目标位置 (tx, ty), 以及其他无人机的位置
        observations = []
        for i in range(self.num_drones):
            obs_i = list(self.drone_positions[i]) + list(self.target_positions[i])
            # 添加其他无人机的位置作为部分观测 (中心化观测的一部分)
            for j in range(self.num_drones):
                if i != j:
                    obs_i.extend(self.drone_positions[j])
            observations.append(np.array(obs_i, dtype=np.float32))
        return observations

    def step(self, actions):
        rewards = [0] * self.num_drones
        self.current_steps += 1

        new_drone_positions = list(self.drone_positions) # 复制当前位置，用于计算新位置

        # 更新无人机位置
        for i, action in enumerate(actions):
            if self.done[i]: # 如果无人机已到达目标，则不再移动
                continue

            dr, dc = ACTIONS[action]
            new_r, new_c = self.drone_positions[i][0] + dr, self.drone_positions[i][1] + dc

            # 检查是否出界
            if 0 <= new_r < self.grid_size and 0 <= new_c < self.grid_size:
                new_drone_positions[i] = (new_r, new_c)
            else:
                # 出界惩罚
                rewards[i] -= 1.0 

        # 检查碰撞 (无人机之间)
        collision_penalty = -5.0 # 碰撞惩罚
        for i in range(self.num_drones):
            for j in range(i + 1, self.num_drones):
                if new_drone_positions[i] == new_drone_positions[j]:
                    rewards[i] += collision_penalty
                    rewards[j] += collision_penalty
                    # 如果发生碰撞，将无人机弹回原位或保持不动，避免卡死
                    new_drone_positions[i] = self.drone_positions[i]
                    new_drone_positions[j] = self.drone_positions[j]

        self.drone_positions = new_drone_positions # 更新实际位置

        # 计算奖励和是否完成
        all_drones_done = True
        for i in range(self.num_drones):
            if self.done[i]:
                rewards[i] += 0.1 # 保持在目标点有小奖励
                continue

            # 距离奖励：越近奖励越高
            dist_to_target = np.linalg.norm(np.array(self.drone_positions[i]) - np.array(self.target_positions[i]))
            rewards[i] += (self.grid_size * 2 - dist_to_target) * 0.1 # 距离越近奖励越大

            # 到达目标点
            if self.drone_positions[i] == self.target_positions[i]:
                rewards[i] += 10.0 # 到达目标点大奖励
                self.done[i] = True
            else:
                all_drones_done = False # 只要有一个没到，就还没完全结束

        # 时间步惩罚，鼓励尽快到达
        for i in range(self.num_drones):
            if not self.done[i]:
                rewards[i] -= 0.05 # 每一步都扣一点点分

        # 判断回合是否结束
        episode_over = all_drones_done or self.current_steps >= self.max_steps

        observations = self._get_observations()
        return observations, rewards, episode_over, {} # info 字典可以放额外信息

# --- 模拟训练循环 (这里是概念性的，真正的 MAPPO 训练会更复杂) ---
if __name__ == "__main__":
    env = DroneEnv()

    print("环境初始化完成。")
    print(f"无人机起始位置: {env.start_positions}")
    print(f"无人机目标位置: {env.target_positions}")

    # 模拟一个简单的策略 (随机移动，仅用于演示)
    # 在真正的 MAPPO 中，这里会是神经网络根据观测输出动作
    def simple_policy(observations):
        actions = []
        for obs in observations:
            # 随机选择一个动作
            actions.append(random.choice(list(ACTIONS.keys())))
        return actions

    num_episodes = 5 # 模拟运行5个回合
    for episode in range(num_episodes):
        observations = env.reset()
        episode_reward = [0] * NUM_DRONES
        done = False

        print(f"\n--- 回合 {episode + 1} 开始 ---")
        print(f"初始无人机位置: {env.drone_positions}")

        while not done:
            # 智能体根据当前观测选择动作 (这里是随机策略)
            actions = simple_policy(observations)

            # 环境执行动作并返回新的观测、奖励、是否结束
            next_observations, rewards, done, _ = env.step(actions)

            for i in range(NUM_DRONES):
                episode_reward[i] += rewards[i]

            observations = next_observations

            print(f"步数: {env.current_steps}, 无人机位置: {env.drone_positions}, 奖励: {rewards}, 完成状态: {env.done}")

            if done:
                print(f"回合 {episode + 1} 结束。总奖励: {episode_reward}")
                print(f"最终无人机位置: {env.drone_positions}")
                print(f"无人机是否到达目标: {env.done}")
                break

    print("\n--- MAPPO 训练概念说明 ---")
    print("上述代码只是一个环境模拟和随机策略演示。")
    print("要实现真正的 MAPPO，你需要：")
    print("1. 定义一个共享的神经网络策略 (Actor-Critic 架构)。")
    print("2. 收集多智能体交互的经验数据 (观测、动作、奖励、下一个观测)。")
    print("3. 使用 PPO 算法的损失函数来更新神经网络参数，优化策略。")
    print("4. 通常会使用像 `Stable Baselines3` (结合 `PettingZoo` 环境接口) 这样的库来简化实现。")
    print("   例如，你可以将 `DroneEnv` 适配成 `PettingZoo` 兼容的环境，然后直接使用 `PPO` 算法进行训练。")
    print("   `PettingZoo` 提供了多智能体环境的标准接口，而 `Stable Baselines3` 提供了 PPO 的实现。")
    print("   MAPPO 的核心思想是：虽然每个智能体独立执行，但它们的策略更新是基于所有智能体的共享信息或中心化评估进行的。")
    print("   这有助于解决多智能体环境中的信用分配问题。")