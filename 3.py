import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import random
from collections import deque
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.animation import FuncAnimation, PillowWriter
import os
import time
from datetime import datetime
import csv

# Grid Environment
class DroneEnvironment:
    def __init__(self, grid_size, num_drones, obstacles):
        self.grid_size = grid_size
        self.num_drones = num_drones
        self.obstacles = set(obstacles)  # Obstacle positions as (x, y) tuples
        self.drones = []  # List of (x, y) positions for drones
        self.targets = []  # List of (x, y) target positions for drones
        self.trajectories = []  # Track drone trajectories for visualization
        self.episode_trajectories = []  # Store trajectories for each episode
        # Enhanced trajectory tracking for data export
        self.detailed_trajectories = []  # Store detailed trajectory data for export
        self.current_episode_data = []  # Current episode detailed data
        self.episode_start_time = None
        self.step_count = 0
        self.reset()

    def reset(self):
        """Reset the environment with random drone and target positions."""
        self.drones = [(i, 0) for i in range(self.num_drones)]  # Start at y=0
        self.targets = [(i, self.grid_size-1) for i in range(self.num_drones)]  # Targets at top
        # Initialize trajectory tracking for new episode
        self.trajectories = [[] for _ in range(self.num_drones)]
        for i in range(self.num_drones):
            self.trajectories[i].append(self.drones[i])

        # Initialize detailed trajectory tracking
        self.current_episode_data = []
        self.episode_start_time = datetime.now()
        self.step_count = 0

        # Record initial positions
        step_data = {
            'step': self.step_count,
            'timestamp': datetime.now(),
            'drone_positions': self.drones.copy(),
            'actions': [None] * self.num_drones,  # No action for initial state
            'rewards': [0.0] * self.num_drones,
            'done': False
        }
        self.current_episode_data.append(step_data)

        return [self.get_state(i) for i in range(self.num_drones)]

    def step(self, actions):
        """Execute actions for all drones and return states, rewards, done."""
        self.step_count += 1
        rewards = []
        next_states = []
        done = True
        new_positions = []

        # Calculate new positions
        for i, (action, (x, y)) in enumerate(zip(actions, self.drones)):
            if action == 0:  # Up
                new_x, new_y = x, min(y + 1, self.grid_size - 1)
            elif action == 1:  # Down
                new_x, new_y = x, max(y - 1, 0)
            elif action == 2:  # Left
                new_x, new_y = max(x - 1, 0), y
            else:  # Right
                new_x, new_y = min(x + 1, self.grid_size - 1), y
            new_positions.append((new_x, new_y))

        # Check collisions and assign rewards
        occupied = set(new_positions)
        for i, new_pos in enumerate(new_positions):
            reward = -0.1  # Small penalty per step
            if new_pos in self.obstacles or (new_pos in occupied and new_positions.count(new_pos) > 1):
                reward = -10  # Collision penalty
            elif new_pos == self.targets[i]:
                reward = 10  # Target reached reward
            else:
                done = False  # Not all drones have reached targets
            rewards.append(reward)
            # Update drone position and trajectory
            if reward != -10:  # Don't move if collision
                self.drones[i] = new_pos
                self.trajectories[i].append(new_pos)
            else:
                self.trajectories[i].append(self.drones[i])  # Stay at current position

        # Record detailed step data for export
        step_data = {
            'step': self.step_count,
            'timestamp': datetime.now(),
            'drone_positions': self.drones.copy(),
            'actions': actions.copy(),
            'rewards': rewards.copy(),
            'done': done
        }
        self.current_episode_data.append(step_data)

        next_states = [self.get_state(i) for i in range(self.num_drones)]
        return next_states, rewards, done

    def get_state(self, drone_id):
        """Return state vector for a drone."""
        x, y = self.drones[drone_id]
        tx, ty = self.targets[drone_id]
        state = [x, y, tx, ty]
        for obs in self.obstacles:
            state.extend(obs)
        for i, (dx, dy) in enumerate(self.drones):
            if i != drone_id:
                state.extend([dx, dy])
        return np.array(state)

    def save_episode_trajectory(self):
        """Save current episode trajectory for visualization and detailed data."""
        self.episode_trajectories.append([traj.copy() for traj in self.trajectories])
        self.detailed_trajectories.append(self.current_episode_data.copy())

    def export_trajectory_data(self, episode, save_path="trajectory_data"):
        """Export detailed trajectory data to text file."""
        if not os.path.exists(save_path):
            os.makedirs(save_path)

        if episode >= len(self.detailed_trajectories):
            return

        episode_data = self.detailed_trajectories[episode]
        filename = f"{save_path}/drone_trajectories_episode_{episode}.txt"

        with open(filename, 'w') as f:
            # Write header information
            f.write(f"Drone Trajectory Data - Episode {episode}\n")
            f.write(f"Episode Start Time: {episode_data[0]['timestamp'].strftime('%Y-%m-%d %H:%M:%S.%f')}\n")
            f.write(f"Grid Size: {self.grid_size}x{self.grid_size}\n")
            f.write(f"Number of Drones: {self.num_drones}\n")
            f.write(f"Obstacles: {list(self.obstacles)}\n")
            f.write(f"Targets: {self.targets}\n")
            f.write(f"Total Steps: {len(episode_data) - 1}\n")  # -1 because first entry is initial state
            f.write("=" * 80 + "\n\n")

            # Write step-by-step data
            f.write("Step\tTimestamp\t\t\t")
            for i in range(self.num_drones):
                f.write(f"Drone{i}_X\tDrone{i}_Y\tAction{i}\tReward{i}\t")
            f.write("Episode_Done\n")
            f.write("-" * 120 + "\n")

            action_names = ['UP', 'DOWN', 'LEFT', 'RIGHT']

            for step_data in episode_data:
                step = step_data['step']
                timestamp = step_data['timestamp'].strftime('%H:%M:%S.%f')[:-3]  # Remove last 3 digits of microseconds
                positions = step_data['drone_positions']
                actions = step_data['actions']
                rewards = step_data['rewards']
                done = step_data['done']

                f.write(f"{step}\t{timestamp}\t\t")
                for i in range(self.num_drones):
                    x, y = positions[i]
                    action_str = action_names[actions[i]] if actions[i] is not None else 'INIT'
                    reward = rewards[i]
                    f.write(f"{x}\t{y}\t{action_str}\t{reward:.1f}\t")
                f.write(f"{done}\n")

            # Write summary statistics
            f.write("\n" + "=" * 80 + "\n")
            f.write("EPISODE SUMMARY\n")
            f.write("=" * 80 + "\n")

            total_rewards = [sum(step_data['rewards'][i] for step_data in episode_data) for i in range(self.num_drones)]
            f.write(f"Total Rewards per Drone: {total_rewards}\n")
            f.write(f"Average Reward per Drone: {[r/(len(episode_data)-1) for r in total_rewards]}\n")
            f.write(f"Episode Duration: {len(episode_data) - 1} steps\n")

            # Final positions
            final_positions = episode_data[-1]['drone_positions']
            f.write(f"Final Positions: {final_positions}\n")
            f.write(f"Target Positions: {self.targets}\n")

            # Check which drones reached targets
            targets_reached = [pos == target for pos, target in zip(final_positions, self.targets)]
            f.write(f"Targets Reached: {targets_reached}\n")
            f.write(f"Success Rate: {sum(targets_reached)}/{self.num_drones} ({100*sum(targets_reached)/self.num_drones:.1f}%)\n")

# Optimized DDQN Network with improved architecture
class DDQN(nn.Module):
    def __init__(self, state_dim, action_dim):
        super(DDQN, self).__init__()
        # Increased network capacity for better learning
        self.fc1 = nn.Linear(state_dim, 256)
        self.fc2 = nn.Linear(256, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, action_dim)
        self.dropout = nn.Dropout(0.1)  # Add dropout for regularization

    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = self.dropout(x)
        x = torch.relu(self.fc2(x))
        x = self.dropout(x)
        x = torch.relu(self.fc3(x))
        return self.fc4(x)

# Optimized Drone Agent with improved hyperparameters
class DroneAgent:
    def __init__(self, state_dim, action_dim):
        self.state_dim = state_dim
        self.action_dim = action_dim
        # Optimized epsilon parameters for faster convergence
        self.epsilon = 0.9  # Start with higher exploration
        self.epsilon_min = 0.05  # Minimum exploration rate
        self.epsilon_decay = 0.995  # Decay rate for epsilon
        self.gamma = 0.95  # Slightly reduced for faster learning
        self.main_net = DDQN(state_dim, action_dim)
        self.target_net = DDQN(state_dim, action_dim)
        self.target_net.load_state_dict(self.main_net.state_dict())
        # Optimized learning rate and optimizer settings
        self.optimizer = optim.Adam(self.main_net.parameters(), lr=0.003, weight_decay=1e-5)
        self.memory = deque(maxlen=20000)  # Increased memory size
        self.loss_history = []  # Track training loss

    def select_action(self, state):
        """Choose an action using epsilon-greedy policy with decay."""
        if random.random() < self.epsilon:
            return random.randint(0, self.action_dim - 1)
        state = torch.FloatTensor(state).unsqueeze(0)
        with torch.no_grad():
            q_values = self.main_net(state)
        return q_values.argmax().item()

    def decay_epsilon(self):
        """Decay epsilon for reduced exploration over time."""
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

    def store_experience(self, state, action, reward, next_state, done):
        """Store experience in replay memory."""
        self.memory.append((state, action, reward, next_state, done))

    def train(self, batch_size):
        """Train the main network using a batch from memory with improved loss tracking."""
        if len(self.memory) < batch_size:
            return None
        batch = random.sample(self.memory, batch_size)
        states, actions, rewards, next_states, dones = zip(*batch)

        states = torch.FloatTensor(states)
        actions = torch.LongTensor(actions)
        rewards = torch.FloatTensor(rewards)
        next_states = torch.FloatTensor(next_states)
        dones = torch.FloatTensor(dones)

        q_values = self.main_net(states).gather(1, actions.unsqueeze(1)).squeeze(1)
        next_actions = self.main_net(next_states).argmax(1)
        next_q_values = self.target_net(next_states).gather(1, next_actions.unsqueeze(1)).squeeze(1)
        targets = rewards + (1 - dones) * self.gamma * next_q_values

        loss = nn.MSELoss()(q_values, targets.detach())
        self.optimizer.zero_grad()
        loss.backward()
        # Gradient clipping for stability
        torch.nn.utils.clip_grad_norm_(self.main_net.parameters(), max_norm=1.0)
        self.optimizer.step()

        # Track loss for monitoring
        self.loss_history.append(loss.item())
        return loss.item()

    def update_target(self):
        """Update target network with main network weights."""
        self.target_net.load_state_dict(self.main_net.state_dict())

# Visualization Functions
def create_animated_trajectory(env, episode, save_path="trajectory_plots", fps=2):
    """Create animated GIF showing drone movement during an episode."""
    if not os.path.exists(save_path):
        os.makedirs(save_path)

    if episode >= len(env.detailed_trajectories):
        print(f"Episode {episode} data not available for animation")
        return

    episode_data = env.detailed_trajectories[episode]
    if len(episode_data) < 2:
        print(f"Episode {episode} has insufficient data for animation")
        return

    # Set up the figure and axis
    fig, ax = plt.subplots(figsize=(10, 10))
    ax.set_xlim(-0.5, env.grid_size - 0.5)
    ax.set_ylim(-0.5, env.grid_size - 0.5)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.set_title(f'Drone Animation - Episode {episode}', fontsize=14, fontweight='bold')
    ax.set_xlabel('X Position', fontsize=12)
    ax.set_ylabel('Y Position', fontsize=12)

    # Draw static elements (obstacles and targets)
    for obs in env.obstacles:
        rect = patches.Rectangle((obs[0]-0.4, obs[1]-0.4), 0.8, 0.8,
                               linewidth=2, edgecolor='red', facecolor='red', alpha=0.7)
        ax.add_patch(rect)

    for i, target in enumerate(env.targets):
        circle = patches.Circle(target, 0.3, linewidth=2, edgecolor='green',
                              facecolor='lightgreen', alpha=0.7)
        ax.add_patch(circle)
        ax.text(target[0], target[1], f'T{i}', ha='center', va='center', fontweight='bold')

    # Colors for different drones
    colors = ['blue', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']

    # Initialize drone markers and trail lines
    drone_markers = []
    trail_lines = []
    step_text = ax.text(0.02, 0.98, '', transform=ax.transAxes, fontsize=12,
                       verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    for i in range(env.num_drones):
        # Drone marker
        marker = ax.plot([], [], 'o', color=colors[i % len(colors)], markersize=12,
                        markeredgecolor='black', markeredgewidth=2, label=f'Drone {i}')[0]
        drone_markers.append(marker)

        # Trail line
        trail = ax.plot([], [], '-', color=colors[i % len(colors)], linewidth=2, alpha=0.6)[0]
        trail_lines.append(trail)

    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    def animate(frame):
        """Animation function for each frame."""
        if frame >= len(episode_data):
            return drone_markers + trail_lines + [step_text]

        step_data = episode_data[frame]
        positions = step_data['drone_positions']
        actions = step_data['actions']
        rewards = step_data['rewards']

        # Update step information text
        action_names = ['UP', 'DOWN', 'LEFT', 'RIGHT']
        info_text = f"Step: {step_data['step']}\n"
        for i in range(env.num_drones):
            action_str = action_names[actions[i]] if actions[i] is not None else 'INIT'
            info_text += f"D{i}: ({positions[i][0]},{positions[i][1]}) {action_str} R:{rewards[i]:.1f}\n"
        step_text.set_text(info_text)

        # Update drone positions and trails
        for i in range(env.num_drones):
            # Update drone marker position
            x, y = positions[i]
            drone_markers[i].set_data([x], [y])

            # Update trail (show path up to current frame)
            trail_x = [episode_data[j]['drone_positions'][i][0] for j in range(frame + 1)]
            trail_y = [episode_data[j]['drone_positions'][i][1] for j in range(frame + 1)]
            trail_lines[i].set_data(trail_x, trail_y)

        return drone_markers + trail_lines + [step_text]

    # Create animation
    anim = FuncAnimation(fig, animate, frames=len(episode_data), interval=1000//fps,
                        blit=True, repeat=True)

    # Save as GIF
    gif_filename = f"{save_path}/drone_animation_episode_{episode}.gif"
    print(f"Creating animation for episode {episode}... (this may take a moment)")

    try:
        writer = PillowWriter(fps=fps)
        anim.save(gif_filename, writer=writer)
        print(f"Animation saved: {gif_filename}")
    except Exception as e:
        print(f"Error saving animation: {e}")
        # Fallback: save as MP4 if available
        try:
            anim.save(f"{save_path}/drone_animation_episode_{episode}.mp4", fps=fps)
            print(f"Animation saved as MP4: {save_path}/drone_animation_episode_{episode}.mp4")
        except:
            print("Could not save animation in any format")

    plt.close(fig)

def plot_trajectory(env, episode, save_path="trajectory_plots"):
    """Plot drone trajectories for a specific episode."""
    if not os.path.exists(save_path):
        os.makedirs(save_path)

    fig, ax = plt.subplots(figsize=(10, 10))

    # Draw grid
    ax.set_xlim(-0.5, env.grid_size - 0.5)
    ax.set_ylim(-0.5, env.grid_size - 0.5)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)

    # Draw obstacles
    for obs in env.obstacles:
        rect = patches.Rectangle((obs[0]-0.4, obs[1]-0.4), 0.8, 0.8,
                               linewidth=2, edgecolor='red', facecolor='red', alpha=0.7)
        ax.add_patch(rect)

    # Draw targets
    for i, target in enumerate(env.targets):
        circle = patches.Circle(target, 0.3, linewidth=2, edgecolor='green',
                              facecolor='lightgreen', alpha=0.7)
        ax.add_patch(circle)
        ax.text(target[0], target[1], f'T{i}', ha='center', va='center', fontweight='bold')

    # Draw trajectories
    colors = ['blue', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
    if episode < len(env.episode_trajectories):
        trajectories = env.episode_trajectories[episode]
        for i, traj in enumerate(trajectories):
            if len(traj) > 1:
                x_coords = [pos[0] for pos in traj]
                y_coords = [pos[1] for pos in traj]
                ax.plot(x_coords, y_coords, color=colors[i % len(colors)],
                       linewidth=2, marker='o', markersize=4, alpha=0.8, label=f'Drone {i}')
                # Mark start and end positions
                ax.plot(x_coords[0], y_coords[0], color=colors[i % len(colors)],
                       marker='s', markersize=8, label=f'Start {i}')
                ax.plot(x_coords[-1], y_coords[-1], color=colors[i % len(colors)],
                       marker='^', markersize=8, label=f'End {i}')

    ax.set_title(f'Drone Trajectories - Episode {episode}', fontsize=14, fontweight='bold')
    ax.set_xlabel('X Position', fontsize=12)
    ax.set_ylabel('Y Position', fontsize=12)
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    plt.tight_layout()
    plt.savefig(f'{save_path}/episode_{episode}_trajectory.png', dpi=300, bbox_inches='tight')
    plt.close()

def plot_training_progress(episode_rewards, episode_losses, save_path="trajectory_plots"):
    """Plot training progress including rewards and losses."""
    if not os.path.exists(save_path):
        os.makedirs(save_path)

    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

    # Plot episode rewards
    episodes = range(len(episode_rewards))
    avg_rewards = [np.mean(rewards) for rewards in episode_rewards]

    ax1.plot(episodes, avg_rewards, 'b-', linewidth=2, label='Average Reward')
    ax1.set_title('Training Progress - Average Reward per Episode', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Episode', fontsize=12)
    ax1.set_ylabel('Average Reward', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.legend()

    # Plot training losses
    if episode_losses:
        ax2.plot(range(len(episode_losses)), episode_losses, 'r-', linewidth=1, alpha=0.7)
        # Add moving average for smoother visualization
        if len(episode_losses) > 50:
            window_size = min(50, len(episode_losses) // 10)
            moving_avg = np.convolve(episode_losses, np.ones(window_size)/window_size, mode='valid')
            ax2.plot(range(window_size-1, len(episode_losses)), moving_avg, 'darkred', linewidth=2, label='Moving Average')

        ax2.set_title('Training Loss Over Time', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Training Step', fontsize=12)
        ax2.set_ylabel('Loss', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend()

    plt.tight_layout()
    plt.savefig(f'{save_path}/training_progress.png', dpi=300, bbox_inches='tight')
    plt.close()

# Optimized Main Training Loop with Visualization
def main():
    print("Starting Optimized Drone Training with Visualization...")

    # Environment setup
    grid_size = 10
    num_drones = 4
    obstacles = [(3, 3), (4, 4), (5, 5)]
    env = DroneEnvironment(grid_size, num_drones, obstacles)
    state_dim = 4 + 2 * len(obstacles) + 2 * (num_drones - 1)  # [x, y, tx, ty, obstacles, other drones]
    action_dim = 4  # Up, Down, Left, Right

    # Initialize agents with optimized parameters
    agents = [DroneAgent(state_dim, action_dim) for _ in range(num_drones)]

    # Optimized training parameters for faster demo
    episodes = 100  # Further reduced for faster demo
    batch_size = 64  # Balanced batch size
    update_freq = 10  # Balanced update frequency

    # Training tracking
    episode_rewards = []
    all_losses = []
    convergence_threshold = 8.0  # Average reward threshold for convergence
    consecutive_good_episodes = 0
    required_consecutive = 10

    print(f"Training Configuration:")
    print(f"- Episodes: {episodes}")
    print(f"- Batch Size: {batch_size}")
    print(f"- Update Frequency: {update_freq}")
    print(f"- Learning Rate: {agents[0].optimizer.param_groups[0]['lr']}")
    print(f"- Network Architecture: 256-256-128-4 with dropout")
    print("-" * 50)

    start_time = time.time()

    for episode in range(episodes):
        states = env.reset()
        done = False
        total_rewards = [0] * num_drones
        episode_losses = []
        step = 0

        while not done and step < 100:  # Reduced max steps for faster episodes
            actions = [agent.select_action(states[i]) for i, agent in enumerate(agents)]
            next_states, rewards, done = env.step(actions)

            # Store experiences and train
            for i, agent in enumerate(agents):
                agent.store_experience(states[i], actions[i], rewards[i], next_states[i], done)
                loss = agent.train(batch_size)
                if loss is not None:
                    episode_losses.append(loss)
                total_rewards[i] += rewards[i]

            states = next_states
            step += 1

            # Update target networks
            if step % update_freq == 0:
                for agent in agents:
                    agent.update_target()

        # Decay epsilon for all agents
        for agent in agents:
            agent.decay_epsilon()

        # Save episode trajectory
        env.save_episode_trajectory()

        # Track training progress
        episode_rewards.append(total_rewards)
        if episode_losses:
            all_losses.extend(episode_losses)

        avg_reward = np.mean(total_rewards)

        # Check for convergence
        if avg_reward >= convergence_threshold:
            consecutive_good_episodes += 1
        else:
            consecutive_good_episodes = 0

        # Progress reporting
        if episode % 10 == 0 or episode < 5:
            elapsed_time = time.time() - start_time
            print(f"Episode {episode:3d} | Avg Reward: {avg_reward:6.2f} | "
                  f"Steps: {step:3d} | Epsilon: {agents[0].epsilon:.3f} | "
                  f"Time: {elapsed_time:.1f}s")

        # Generate trajectory plots and export data for selected episodes
        if episode % 20 == 0 or episode < 3:
            plot_trajectory(env, episode)
            # Export trajectory data to text file
            env.export_trajectory_data(episode)
            # Create animated GIF (for first few episodes and every 20th episode)
            if episode < 5 or episode % 40 == 0:  # Less frequent animation to save time
                create_animated_trajectory(env, episode, fps=3)

        # Early stopping if converged
        if consecutive_good_episodes >= required_consecutive:
            print(f"\nConverged after {episode + 1} episodes!")
            print(f"Average reward maintained above {convergence_threshold} for {required_consecutive} consecutive episodes.")
            break

    total_time = time.time() - start_time
    print(f"\nTraining completed in {total_time:.2f} seconds")
    print(f"Final average reward: {np.mean(episode_rewards[-1]):.2f}")
    print(f"Final epsilon: {agents[0].epsilon:.3f}")

    # Generate final visualizations and data exports
    print("\nGenerating training progress plots...")
    plot_training_progress(episode_rewards, all_losses)

    # Plot final episode trajectory and export data
    final_episode = len(env.episode_trajectories) - 1
    if final_episode >= 0:
        print(f"\nGenerating final episode ({final_episode}) visualizations...")
        plot_trajectory(env, final_episode)
        env.export_trajectory_data(final_episode)
        create_animated_trajectory(env, final_episode, fps=3)

        print(f"Trajectory plots saved in 'trajectory_plots' directory")
        print(f"Trajectory data exported to 'trajectory_data' directory")

        # Export data for a few key episodes
        key_episodes = [0, len(env.episode_trajectories) // 4, len(env.episode_trajectories) // 2,
                       3 * len(env.episode_trajectories) // 4, final_episode]
        key_episodes = list(set([ep for ep in key_episodes if 0 <= ep < len(env.episode_trajectories)]))

        print(f"\nExporting trajectory data for key episodes: {key_episodes}")
        for ep in key_episodes:
            if ep != final_episode:  # Already exported above
                env.export_trajectory_data(ep)

    print("\nTraining, visualization, and data export complete!")
    print(f"Check the following directories:")
    print(f"  - 'trajectory_plots/': Static plots and GIF animations")
    print(f"  - 'trajectory_data/': Detailed trajectory data in text format")

if __name__ == "__main__":
    main()